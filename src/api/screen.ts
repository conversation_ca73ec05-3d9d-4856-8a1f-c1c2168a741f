import req from '@/utils/req'

const ScreenApi = {
  // 专题树列表
  getThematicFeatureTree(params?: any) {
    return req({
      url: `/thematicFeatureScreen/treeList`,
      method: 'get',
      params
    })
  },
  // 专题分类树列表
  getThematicFeatureCategoryTree(params: any) {
    return req({
      url: `/thematicFeatureScreen/classTreeList`,
      method: 'get',
      params
    })
  },
  // 专题数据列表
  getThematicFeatureDataList(params: any) {
    return req({
      url: `/thematicFeatureScreen/featureDataList`,
      method: 'get',
      params
    })
  },
  // 指标分类列表
  getNormClassifyList(params: any) {
    return req({
      url: `/indexScreen/classList`,
      method: 'get',
      params
    })
  },
  // 指标数据列表
  getNormDataList(params: any) {
    return req({
      url: `/indexScreen/indexDataList`,
      method: 'get',
      params
    })
  },
  // 特色产品分类统计
  getFeatureClassifyStatistics(params: any) {
    return req({
      url: `/thematicProductScreen/count`,
      method: 'get',
      params
    })
  },
  // 特色产品点位数据
  getFeatureProductDataList(params: any) {
    return req({
      url: `/thematicProductScreen/list`,
      method: 'get',
      params
    })
  },
  // 特色产品详情
  getFeatureProductDetail(id: any) {
    return req({
      url: `/thematicProductScreen/queryInfo/${id}`,
      method: 'get'
    })
  },
  // 产业链分类数据
  getIndustryChainDataList() {
    return req({
      url: `/thematicIndustryData/classificationData`,
      method: 'get'
    })
  },
  // 产业链点位数据
  getIndustryChainPointDataList(params?: any) {
    return req({
      url: `/thematicIndustryData/coreIndustries`,
      method: 'get',
      params
    })
  },
  // 产业链企业数据
  getIndustryChainEnterpriseDataList(params?: any) {
    return req({
      url: `/thematicIndustryData/coreEnterprise`,
      method: 'get',
      params
    })
  },
  // 产业链项目数据
  getIndustryChainProjectDataList(params?: any) {
    return req({
      url: `/thematicIndustryData/keyProjects`,
      method: 'get',
      params
    })
  },
  // 产业链详情
  getThematicIndustryDetail(params: any) {
    return req({
      url: `/thematicIndustryData/industryChainDetails`,
      method: 'get',
      params
    })
  },
  // 产业链产值/核心指标
  getThematicIndustryData(params: any) {
    return req({
      url: `/thematicIndustryData/coreIndicators`,
      method: 'get',
      params
    })
  },
  // 产业链企业详情
  getThematicIndustryEnterpriseDetail(id: string) {
    return req({
      url: `/thematicIndustryData/enterpriseDetails/${id}`,
      method: 'get'
    })
  },
  // 重点项目点位数据
  getKeyProjectPointDataList(params: any) {
    return req({
      url: `/thematicProjectScreen/list`,
      method: 'get',
      params
    })
  },
  // 重点项目详情
  getKeyProjectDetail(id: string) {
    return req({
      url: `/thematicProjectScreen/queryInfo/${id}`,
      method: 'get'
    })
  },
}

export default ScreenApi
