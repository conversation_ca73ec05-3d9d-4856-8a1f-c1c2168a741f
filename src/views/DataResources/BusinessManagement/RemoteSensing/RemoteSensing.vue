<template>
  <z-page>
    <z-table
      row-key="id"
      ref="tableRef"
      v-model:query="query"
      :columns="columns"
      :fetch-data="fetchTableData"
      paginationMode="server"
      show-toolbar
      :toolbarRows="toolbarRows"
    >
      <template #beforePicPath="{ row }">
        <el-image :src="ASSETS_URL + row.beforePicPath" preview-teleported :preview-src-list="[ASSETS_URL + row.beforePicPath]" class="w-[80px] h-[80px]" />
      </template>
      <template #endPicPath="{ row }">
        <el-image :src="ASSETS_URL + row.endPicPath" preview-teleported :preview-src-list="[ASSETS_URL + row.endPicPath]" class="w-[80px] h-[80px]" />
      </template>
      <template #operation="{ row }">
        <el-button
          size="small"
          type="primary"
          link
          :disabled="row.status === 1"
          @click="dialog.open('turnClues', { id: row.id })"
        >转为线索
        </el-button>
        <el-popconfirm
          :title="t('message.confirmDelete')"
          placement="top-start"
          @confirm="onDelete(row)"
        >
          <template #reference>
            <el-button
              size="small"
              type="danger"
              link
            >{{ t('common.delete') }}
            </el-button>
          </template>
        </el-popconfirm>
      </template>
    </z-table>

    <z-dialog-form
      v-model:visible="dialog.visible.value"
      :title="dialog.title.value"
      width="600px"
      @confirm="dialog.handleConfirm"
      @cancel="dialog.handleCancel"
      @close="dialog.handleClose"
    >
      <template v-if="dialog.mode.value === 'create'">
        <RemoteSensingForm :model="dialog.model.value" :mode="dialog.mode.value" />
      </template>
      <template v-if="dialog.mode.value === 'turnClues'">
        <TurnClues :model="dialog.model.value" :mode="dialog.mode.value" />
      </template>
      <template v-if="dialog.mode.value === 'import'">
        <ImportForm :mode="dialog.mode.value" />
      </template>
    </z-dialog-form>
  </z-page>
</template>

<script setup lang="ts">
import { useTypedI18n } from '@/i18n'
import { ref } from 'vue'
import type {
  ZTableColumn,
  ZTableInstance,
  ZTableParams,
  ZTableToolbarRow
} from '@/ui/components/ZTable/types.ts'
import dataResApi from '@/api/dataResources.ts'
import { ZTableToolbarFactory } from '@/ui/components/ZTable/factories.ts'
import { useDialog } from '@/composables/useDialog.ts'
import { ElMessage } from 'element-plus'
import DictApI from '@/api/dict.ts'
import RemoteSensingForm from './components/RemoteSensingForm.vue'
import TurnClues from './components/TurnClues.vue'
import ImportForm from './components/ImportForm.vue'
import ComApi from '@/api/common.ts'

const { t } = useTypedI18n()

const ASSETS_URL = import.meta.env.VITE_STATIC_ASSETS_URL

const tableRef = ref<ZTableInstance | null>(null)

const toolbarRows: ZTableToolbarRow[] = [
  {
    tools: [
      ZTableToolbarFactory.createAdd({
        onClick: () => {
          dialog.open('create')
        }
      }),
      ZTableToolbarFactory.createImport({
        onClick: () => {
          dialog.open('import')
        }
      })
    ]
  }
]

const query = ref()
const columns = ref<ZTableColumn[]>([
  {
    prop: 'dataType',
    label: '图斑类型',
    asyncFormatter: {
      cacheKey: 'anyQuestionTypeValue',
      async fetch() {
        const { data } = await DictApI.getDictItemsByCode('questiontypes')
        return data
      },
      render: (value) => (row) => value.find((it: any) => it.dictionaryCode === row.dataType)?.dictionaryName,
    },
  },
  { prop: 'geom', label: '位置', showOverflowTooltip: true },
  {
    prop: 'regionCode',
    label: '所属行政区县',
    asyncFormatter: {
      cacheKey: 'regionCodeValue',
      async fetch() {
        return await ComApi.getAreaList().then(res => res.data)
      },
      render: (value) => (row) => value.find((it: any) => it.areaCode === row.regionCode)?.areaName,
    },
  },
  { prop: 'area', label: '图斑面积' },
  { prop: 'beforePicTime', label: '前时相时间' },
  { prop: 'beforePicPath', label: '前时相影像' },
  { prop: 'endPicTime', label: '后时相时间' },
  { prop: 'endPicPath', label: '后时相影像' },
  { prop: 'operation', label: '操作', align: 'center', width: 180, fixed: 'right' },
])

const fetchTableData = async (params: ZTableParams) => {
  return dataResApi.getRemoteDataList(params).then((res) => res.data)
}

const dialog = useDialog({
  name: '遥感监测',
  modeMap: {
    turnClues: '转为线索',
    import: '导入'
  },
  onConfirm(mode, ctx) {
    ctx.form?.validate((isValid, values) => {
      if (isValid) {
        if (mode === 'create') {
          ctx.isSubmitting.value = true
          dataResApi.saveRemoteData(values)
            .then(() => {
              tableRef.value?.refresh()
              dialog.close()
              ElMessage.success(t('message.operationSuccess'))
            })
            .finally(() => {
              ctx.isSubmitting.value = false
            })
        }
        if (mode === 'turnClues') {
          ctx.isSubmitting.value = true
          dataResApi.updateRemoteData(values)
            .then(() => {
              tableRef.value?.refresh()
              dialog.close()
              ElMessage.success(t('message.operationSuccess'))
            })
            .finally(() => {
              ctx.isSubmitting.value = false
            })
        }

        if (mode === 'import') {
          ctx.isSubmitting.value = true
          const formData = new FormData()
          formData.append('file', values.file.raw)
          dataResApi.importRemoteData(formData)
            .then(() => {
              tableRef.value?.refresh()
              dialog.close()
              ElMessage.success(t('message.operationSuccess'))
            })
            .finally(() => {
              ctx.isSubmitting.value = false
            })
        }
      }
    })
  },
})

const onDelete = (row: any) => {
  dataResApi.deleteRemoteData(row.id).then(() => {
    tableRef.value?.refresh()
    ElMessage.success(t('message.deleteSuccess'))
  })
}
</script>

<style scoped lang="scss">

</style>
