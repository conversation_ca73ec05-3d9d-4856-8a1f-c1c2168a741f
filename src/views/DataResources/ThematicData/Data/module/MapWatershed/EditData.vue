<template>
  <z-page class="pt-2">
    <z-form
      ref="formRef"
      v-model:model="form"
      :fields="fields"
      :validate-on-rule-change="false"
      label-width="140px"
      :fetch-data="fetchData"
      :showFooter="true"
      @submit="handleSubmit"
      @reset="reset"
    >
      <!-- 所属分类 -->
      <template #item-thematicClassId="{ formData, field }">
        <el-tree-select
          v-model="formData[field.name]"
          :data="categoryData"
          node-key="thematicClassId"
          :props="{
            label: 'thematicClassName',
            children: 'children',
          }"
          multiple
          :render-after-expand="false"
          style="width: 560px"
        />
      </template>
      <!-- 位置 -->
      <template #item-locationType="{ formData, field }">
        <div class="w-full">
          <el-radio-group
            v-model="formData[field.name]"
            @change="onChangeType"
          >
            <el-radio :value="LocationType.point">点</el-radio>
            <el-radio :value="LocationType.line">线</el-radio>
            <el-radio :value="LocationType.polygonal">面</el-radio>
          </el-radio-group>
          <br />
          <el-input
            style="width: 560px; margin-bottom: 10px"
            v-model="formData['locationName']"
            placeholder="请输入位置"
          />
          <template v-if="!currentId">
            <map-view
              :location-type="formData[field.name]"
              :locationName="formData['locationName']"
              :position="formData['location']"
              @setPosition="setPosition"
              @setName="setName"
              @setPoint="setPoint"
            />
          </template>
          <template v-if="currentId && showMap">
            <map-view
              :location-type="formData[field.name]"
              :locationName="formData['locationName']"
              :position="formData['location']"
              @setPosition="setPosition"
              @setName="setName"
              @setPoint="setPoint"
            />
          </template>
        </div>
      </template>
      <!-- 实景图片 -->
      <template #item-imgUrl="{ validate, value, updateValue }">
        <z-upload
          class="w-140! avatar-uploader"
          :allowed-types="[FileType.IMAGE]"
          :show-file-list="true"
          :limit="9"
          :max-size="5"
          tip="支持格式：jpg/jpeg/png ，最多上传9张，单张图片大小不超过5MB"
          @success="
            (r, file, newFileList) => handleUploadSuccess(r, { validate, updateValue }, newFileList)
          "
        >
        </z-upload>
      </template>
      <!-- 实景视频 -->
      <template #item-videoUrl="{ validate, value, updateValue }">
        <z-upload
          class="w-140!"
          :allowed-types="[FileType.VIDEO]"
          :model-value="value"
          :show-file-info="true"
          :show-file-list="false"
          :limit="1"
          :max-size="1024"
          tip="支持格式：mp4/mov/mkv/wmv，最多上传1个视频，每个视频大小不超过1G"
          @success="
            (r, file, newFileList) => handleUploadSuccess(r, { validate, updateValue }, newFileList)
          "
        >
        </z-upload>
      </template>
      <!-- 实时监控 -->
      <template #item-control="{ formData, field }">
        <el-select
          v-model="formData[field.name]"
          multiple
          placeholder="请选择监控设备，支持多选"
          style="width: 560px"
        >
          <el-option
            v-for="item in equipmentData"
            :key="item.monitorDeviceCode"
            :label="item.monitorDeviceName"
            :value="item.monitorDeviceCode"
          />
        </el-select>
      </template>
      <!-- 简介 -->
      <template #item-summary="{ validate, value, updateValue }">
        <z-editor
          :model-value="value"
          @change="(html) => handleEditorSuccess(html, { validate, updateValue })"
        />
      </template>
      <!-- 相关文件 -->
      <template #item-file="{ validate, value, updateValue }">
        <z-upload
          class="w-140!"
          :show-file-info="false"
          :show-preview="false"
          :allowed-types="[FileType.OTHER]"
          :file-type-map="{
            other: ['doc', 'docx', 'xlsx', 'xls', 'pdf'],
          }"
          :limit="5"
          :max-size="5"
          tip="支持格式：doc、docx、xlsx、xls、pdf，最多上传5个附件，每个附件大小超过5M"
          @success="
            (r, file, newFileList) => handleUploadSuccess(r, { validate, updateValue }, newFileList)
          "
        >
        </z-upload>
      </template>
    </z-form>
  </z-page>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted, nextTick } from 'vue'
import { useRoute } from 'vue-router'
import type { ZFormField, ZFormInstance } from '@/ui/components/ZForm/types'
import { LocationType } from '@/utils/enums'
import ComApi from '@/api/common'
import dataResApi from '@/api/dataResources'
import { FileType } from '@/ui/components/ZUpload/constants'
import { ElMessage } from 'element-plus'
import MapView from './MapView.vue'
import router from '@/router'

interface ThematicClass {
  thematicClassId: number
  thematicClassName: string
  children?: ThematicClass[]
  parent?: ThematicClass
  thematicDataId?: string
  thematicClassLevelCode?: string
  [key: string]: unknown
}

const formRef = ref<ZFormInstance>()

const form = reactive({
  thematicFeatureDataId: 0,
  locationType: '1',
  displayed: false,
  location: '',
  locationName: '',
})

const fields = ref<ZFormField[]>([
  {
    name: 'thematicFeatureDataName',
    label: '名称',
    type: 'input',
    fieldClass: 'w-[560px]',
    rules: [
      {
        required: true,
        message: '请输入名称',
        trigger: 'blur',
      },
    ],
    props: {
      placeholder: '请输入名称',
      maxlength: 50,
      showWordLimit: true,
      clearable: true,
    },
  },
  {
    name: 'thematicClassId',
    label: '所属分类',
    type: 'custom',
    fieldClass: 'w-[560px]',
    rules: [
      {
        required: true,
        message: '请选择所属分类',
        trigger: 'change',
      },
    ],
  },
  {
    name: 'areaCode',
    label: '所属行政区县',
    type: 'tree-select',
    fieldClass: 'w-[560px]',
    // multiple: true,
    props: {
      checkStrictly: true,
      placeholder: '请选择所属行政区县',
    },
    valueKey: 'areaCode',
    treeProps: {
      label: 'areaName',
      children: 'children',
    },
    rules: [
      {
        required: true,
        message: '请选择所属分类',
        trigger: 'change',
      },
    ],
    noCache: false,
    treeOptions: async (context) => {
      return ComApi.getAreaList().then((res) => res.data)
    },
  },
  {
    name: 'locationType',
    label: '位置',
    type: 'custom',
    rules: [
      {
        required: true,
        message: '请选择位置',
        trigger: 'change',
      },
    ],
  },
  {
    name: 'locationName',
    label: '位置',
    type: 'input',
    show: false,
  },
  {
    name: 'location',
    type: 'input',
    label: '坐标',
    show: false,
  },
  {
    name: 'displayed',
    label: '是否在地图显示',
    type: 'radio',
    rules: [
      {
        required: true,
        message: '请选择',
      },
    ],
    options: [
      {
        label: '是',
        value: true,
      },
      {
        label: '否',
        value: false,
      },
    ],
  },
  {
    name: 'summary',
    label: '简介',
    type: 'custom',
    fieldClass: 'w-[560px]',
  },
  {
    name: 'imgUrl',
    label: '实景图片',
    type: 'custom',
  },
  {
    name: 'videoUrl',
    label: '实景视频',
    type: 'custom',
  },
  {
    name: 'control',
    label: '实时监控',
    type: 'custom',
  },
  {
    name: 'file',
    label: '相关文件',
    type: 'custom',
  },
])
const equipmentData = ref<any[]>([])

/**
 * 根据 thematicClassId 集合匹配节点
 * @param data 源数据（树形结构）
 * @param ids 需要匹配的 thematicClassId 数组
 * @returns 匹配到的节点数组
 */
const findNodesByIds = (data: ThematicClass[], ids: number[] = []): ThematicClass[] => {
  const result: ThematicClass[] = []

  // 递归查找函数
  const traverse = (nodes: ThematicClass[]) => {
    nodes.forEach((node) => {
      if (ids.includes(node.thematicClassId)) {
        result.push({
          thematicClassId: node.thematicClassId,
          thematicClassName: node.thematicClassName,
          thematicClassLevelCode: node.thematicClassLevelCode,
          thematicDataId: node.thematicId as string,
        }) // 匹配到则加入结果
      }
      if (node.children) {
        traverse(node.children) // 递归子节点
      }
    })
  }

  traverse(data)
  return result
}

const findEquimpentData = (data: any[], ids: any[] = []) => {
  const result: any[] = []
  // 递归查找函数
  const traverse = (nodes: any[]) => {
    nodes.forEach((node) => {
      if (ids.includes(node.monitorDeviceCode)) {
        result.push({
          monitorCode: node.monitorDeviceCode,
          monitorName: node.monitorDeviceName,
        }) // 匹配到则加入结果
      }
    })
  }
  traverse(data)
  return result
}
const buildParentRelation = (nodes: ThematicClass[], parent: ThematicClass | null = null) => {
  nodes.forEach((node) => {
    node.parent = parent || undefined
    if (node.children) {
      buildParentRelation(node.children, node)
    }
  })
}

const handleUploadSuccess = (r: any, { validate, updateValue }: any, newFileList: any[]) => {
  updateValue(newFileList.map((it: any) => it?.response?.data?.absUrl || it.name))
  // updateValue(newFileList.map((it: any) => {
  //   return {
  //     fileUrl: it?.response?.data?.absUrl || it.name,
  //     fileName: it?.response?.data?.fileName
  //   }
  // }))
  validate()
}
const handleEditorSuccess = (r: any, { validate, updateValue }: any) => {
  updateValue(r)
  validate()
}
const currentId = ref('')
const showMap = ref<boolean>(false)
const fetchData = async () => {
  const formData = await getDetails(currentId.value)
  const processedData = {
    ...formData,
    thematicClassId: formData.thematicFeatureDataClasses?.map((i: any) => i.thematicClassId) ?? [],
    imgUrl: formData.pictureList?.map((i: any) => i.pictureUrl) ?? [],
    videoUrl: formData.videoList?.map((i: any) => i.videoUrl) ?? [],
    file: formData.attachmentList?.map((i: any) => i.attachmentUrl) ?? [],
    control: formData.monitorList?.map((i: any) => i.monitorCode) ?? [],
  }
  nextTick(() => {
    showMap.value = true
  })
  return processedData
}
const getDetails = async (id: string) => {
  return dataResApi.getThematicFeatureDataDetaile(id).then((res) => {
    return res.data
  })
}
const reset = () => {
  router.go(-1)
}
const handleSubmit = async () => {
  try {
    // 验证表单
    const isValid = await formRef.value?.validate()
    if (!isValid) return

    // 获取表单数据
    const formValues = formRef.value?.getValues()
    if (!formValues) return
    // 检查地理位置
    if (!formValues.location) {
      const locationTypes = { '1': '点', '2': '线', '3': '面' }
      const text = (locationTypes as any)[formValues.locationType] || '位置'
      ElMessage.warning(`请在地图上添加${text}`)
      return
    }

    // 并行处理数据
    const [categoryNodes, areaList] = await Promise.all([
      Promise.resolve(findNodesByIds(categoryData.value, formValues.thematicClassId)),
      ComApi.getAreaList().then((res) => res.data),
    ])

    // 查找当前区域
    const currentArea = areaList.find((area: any) => area.areaCode === formValues.areaCode)
    if (!currentArea) {
      ElMessage.warning('未找到对应的行政区域')
      return
    }

    // 处理文件列表
    const createFileList = (items: string[] = [], type: string) =>
      items.map((file) => ({
        [`${type}Name`]: file,
        [`${type}Url`]: file,
      }))

    // 构建提交数据
    const submitData = {
      ...formValues,
      areaName: currentArea.areaName,
      areaId: currentArea.areaId,
      thematicFeatureDataClasses: categoryNodes,
      pictureList: createFileList(formValues.imgUrl, 'picture'),
      videoList: createFileList(formValues.videoUrl, 'video'),
      attachmentList: createFileList(formValues.file, 'attachment'),
      monitorList: findEquimpentData(equipmentData.value, formValues.control),
    }

    // 根据ID决定是更新还是新增
    const apiMethod = currentId.value
      ? dataResApi.updateThematicFeatureData({
          ...submitData,
          thematicFeatureDataId: currentId.value,
        })
      : dataResApi.addThematicFeatureData(submitData)

    // 执行API调用
    const res = await apiMethod
    ElMessage.success(res.message)
    router.go(-1)
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败，请稍后重试')
  }
}
const setPoint = (option: any) => {
  const values = formRef.value?.getValues()
  Object.assign(form, {
    ...form,
    ...values,
    location: option.position ? JSON.stringify(option.position) : '',
    locationName: option.name ? option.name : '',
  })
}
const setPosition = (position: any[]) => {
  const values = formRef.value?.getValues()
  Object.assign(form, {
    ...form,
    ...values,
    location: position.length ? JSON.stringify(position) : '',
  })
}
const setName = (name: string) => {
  const values = formRef.value?.getValues()
  Object.assign(form, { ...form, ...values, locationName: name })
}

const onChangeType = (value: any) => {
  const values = formRef.value?.getValues()
  Object.assign(form, { ...form, ...values, location: '', locationName: '' })
}
const categoryData = ref<ThematicClass[]>([])

// 加载数据
const loadData = async () => {
  const res = await dataResApi.getCategoryData()
  categoryData.value = res.data
  buildParentRelation(categoryData.value) // 确保数据加载完成后再调用
  equipmentData.value = await dataResApi.getDeviceManageAll().then((res) => res.data)
}

onMounted(async () => {
  const route = useRoute()
  currentId.value = (route.query.id as string) || ''
  await loadData()
  if (currentId.value) {
    formRef.value?.refetch()
  }
})
</script>
