<template>
  <z-page>
    <z-form
      v-model:model="form"
      ref="formRef"
      :fields="fields"
      label-width="120px"
    >
      <template #item-questionImg="{ validate, value, updateValue }">
        <z-upload
          class="w-140!"
          :allowed-types="[FileType.IMAGE]"
          :model-value="value"
          :limit="9"
          :max-size="5"
          tip="支持格式：jpg/jpeg/png ，最多上传9张，单张图片大小不超过5MB"
          @success="
            (r, file, newFileList) => handleUploadSuccess(r, { validate, updateValue }, newFileList)
          "
        >
        </z-upload>
      </template>
      <template #item-questionFile="{ validate, value, updateValue }">
        <z-upload
          class="w-140!"
          :model-value="value"
          tip="支持格式：doc、docx、xlsx、xls、pdf文件，最多上传5个附件，每个附件大小超过5M"
          :show-file-info="false"
          :show-preview="false"
          :allowed-types="[FileType.OTHER]"
          :file-type-map="{
            other: ['doc', 'docx', 'xlsx', 'xls', 'pdf'],
          }"
          :limit="5"
          :max-size="5"
          @success="
            (r, file, newFileList) => handleUploadSuccess(r, { validate, updateValue }, newFileList)
          "
        >
        </z-upload>
      </template>
      <template #item-questionLocation>
        <div class="w-170 h-[300px] relative">
          <TMapView
            class="z-1"
            ref="mapRef"
            :options="mapOptions"
            @ready="onMapReady"
          />
          <el-button
            class="absolute right-5 top-5 z-2"
            @click="toggleMarkTool"
            :type="isMarkToolActive ? 'primary' : ''"
            :icon="Location"
          >
            {{ isMarkToolActive ? '停止标注' : '开始标注' }}
          </el-button>
        </div>
      </template>
      <template #item-flowList="{ validate, value, updateValue }">
        <div class="flex-col w-100">
          <div
            v-for="(v, i) in value"
            :key="i"
            class="flex-col mb-5"
          >
            <div class="flex items-center">
              <el-icon><User /></el-icon>
              <span class="whitespace-nowrap mx-2"
                >{{ NumberChineseConverter.toChinese(i + 1) }}级审批人</span
              >
              <z-select
                v-model="v.approveAccountId"
                :fetch-options="getUserOptions"
                placeholder="请输入审批人"
              ></z-select>
              <el-icon
                class="ml-4 cursor-pointer c-[#FF0066]"
                @click="() => updateValue(value.filter((_: any, index: number) => index !== i))"
                ><Delete
              /></el-icon>
            </div>
          </div>
          <el-button
            v-if="value.length < 3"
            :icon="Plus"
            type="primary"
            plain
            @click="() => updateValue([...value, { approveAccountId: '' }])"
            >添加审批人</el-button
          >
        </div>
      </template>
    </z-form>
    <div class="flex justify-center mt-10">
      <el-button @click="goBack()">{{ t('common.cancel') }}</el-button>
      <el-button
        :loading="isSubmitting"
        type="primary"
        @click="submit('0')"
        >保存草稿</el-button
      >
      <el-button
        :loading="isExamine"
        type="primary"
        @click="submit('1')"
        >提交审核</el-button
      >
    </div>
  </z-page>
</template>

<script setup lang="ts">
import { computed, onMounted, reactive, ref } from 'vue'
import type { ZFormField } from '@/ui/components/ZForm/types.ts'
import DictApI from '@/api/dict.ts'
import SupervisionApi, { DictCodes } from '@/api/supervision.ts'
import ComApi from '@/api/common.ts'
import { User, Delete, Location } from '@element-plus/icons-vue'
import { Plus } from '@element-plus/icons-vue'
import UserApi from '@/api/user.ts'
import { useNavigation } from '@/composables/useNavigation.ts'
import { useRoute } from 'vue-router'
import { useTypedI18n } from '@/i18n'
import { ElButton, ElMessage } from 'element-plus'
import { NumberChineseConverter } from '@/utils/option.ts'
import { FileType } from '@/ui/components/ZUpload/constants'
import dayjs from 'dayjs'
import {
  TAnchorPosition,
  TMap,
  TMapView,
  TPointMarkerMode,
  type TPointMarkerTool,
  TSearchType,
  TSvgOverlay,
} from '@/map'
import { BJ_CENTER_POINT } from '@/config/default-settings.ts'

const { goBack } = useNavigation()
const route = useRoute()
const { t } = useTypedI18n()

const formRef = ref()

const form = reactive({
  flowList: [],
  questionLocation: '',
  questionLocationName: '',
})

const fields = ref<ZFormField[]>([
  {
    type: 'input',
    name: 'questionNo',
    label: '问题编号',
    fieldClass: 'w-100',
    props: {
      placeholder: '请输入问题编号',
      maxlength: 50,
      showWordLimit: true,
      clearable: true,
    },
    rules: [{ required: true, message: '请输入问题编号', trigger: 'change' }],
  },
  {
    type: 'input',
    name: 'projectName',
    label: '项目名称',
    fieldClass: 'w-100',
    props: {
      placeholder: '请输入项目名称',
      maxlength: 50,
      showWordLimit: true,
      clearable: true,
    },
    rules: [{ required: true, message: '请输入项目名称', trigger: 'change' }],
  },
  {
    type: 'select',
    name: 'questionType',
    label: '问题类型',
    fieldClass: 'w-100',
    props: {
      placeholder: '请选择问题类型',
    },
    rules: [{ required: true, message: '请选择问题类型', trigger: 'change' }],
    options: () => DictApI.getDictItemsByCode(DictCodes.QuestionTypes).then((res) => res.data),
    valueKey: 'dictionaryCode',
    labelKey: 'dictionaryName',
  },
  {
    type: 'radio',
    name: 'exigenceLevel',
    label: '紧急程度',
    fieldClass: 'w-100',
    rules: [{ required: true, message: '请选择紧急程度', trigger: 'change' }],
    options: () =>
      DictApI.getDictItemsByCode(DictCodes.Urgency).then((res) =>
        res.data.map((it: any) => ({
          label: it.dictionaryName,
          value: it.dictionaryCode,
        })),
      ),
  },
  {
    type: 'input',
    name: 'questionContent',
    label: '存在问题',
    fieldClass: 'w-140',
    props: {
      placeholder: '请输入存在问题',
      maxlength: 1000,
      showWordLimit: true,
      type: 'textarea',
      autosize: { minRows: 2, maxRows: 4 },
    },
    rules: [{ required: true, message: '请输入存在问题', trigger: 'change' }],
  },
  {
    type: 'input',
    name: 'completeTarget',
    label: '整改目标',
    fieldClass: 'w-140',
    props: {
      placeholder: '请输入整改目标',
      maxlength: 1000,
      showWordLimit: true,
      type: 'textarea',
      autosize: { minRows: 2, maxRows: 4 },
    },
    rules: [{ required: true, message: '请输入整改目标', trigger: 'change' }],
  },
  {
    type: 'custom',
    name: 'questionImg',
    label: '问题图片',
  },
  {
    type: 'custom',
    name: 'questionFile',
    label: '附件',
  },
  {
    type: 'custom',
    name: 'questionLocation',
    label: '位置',
    rules: [{ required: true, message: '请输入位置', trigger: 'blur' }],
  },
  {
    type: 'custom',
    name: 'questionLocationName',
    label: '位置名称',
    show: false,
  },
  {
    type: 'select',
    name: 'questionArea',
    label: '所属行政区县',
    fieldClass: 'w-100',
    props: {
      placeholder: '请选择所属行政区县',
    },
    rules: [{ required: true, message: '请选择所属行政区县', trigger: 'change' }],
    options: () =>
      ComApi.getAreaList().then((res) =>
        res.data.map((it: any) => ({
          label: it.areaName,
          value: it.areaCode,
        })),
      ),
  },
  {
    type: 'date',
    name: 'startDate',
    label: '任务开始日期',
    fieldClass: 'w-100!',
    props: {
      placeholder: '请选择任务开始日期',
    },
    rules: [{ required: true, message: '请选择任务开始日期', trigger: 'change' }],
  },
  {
    type: 'date',
    name: 'endDate',
    label: '预计完成日期',
    fieldClass: 'w-100!',
    props: {
      placeholder: '请选择预计完成日期',
      disabledDate: (time: any) => time.getTime() < Date.now(),
    },
    rules: [{ required: true, message: '请选择预计完成日期', trigger: 'change' }],
  },
  {
    type: 'radio',
    name: 'reportPeriod',
    label: '整改上报周期',
    fieldClass: 'w-100',
    rules: [{ required: true, message: '请选择整改上报周期', trigger: 'change' }],
    cacheKey: 'reportPeriod',
    options: () =>
      DictApI.getDictItemsByCode(DictCodes.PeriodCycle).then((res) =>
        res.data.map((it: any) => ({
          label: it.dictionaryName,
          value: it.dictionaryCode,
        })),
      ),
  },
  {
    type: 'tree-select',
    name: 'dutyDeptId',
    label: '责任部门',
    fieldClass: 'w-100',
    props: {
      placeholder: '请选择责任部门',
      checkStrictly: true,
    },
    rules: [{ required: true, message: '请选择责任部门', trigger: 'change' }],
    treeOptions: () => ComApi.getDeptTree().then((res) => res.data),
    treeProps: {
      label: 'departmentName',
      children: 'children',
    },
    valueKey: 'dataId',
  },
  {
    type: 'tree-select',
    name: 'superviseDeptId',
    label: '督办部门',
    fieldClass: 'w-100',
    props: {
      placeholder: '请选择督办部门',
      checkStrictly: true,
    },
    rules: [{ required: true, message: '请选择督办部门', trigger: 'change' }],
    treeOptions: () => ComApi.getDeptTree().then((res) => res.data),
    treeProps: {
      label: 'departmentName',
      children: 'children',
    },
    valueKey: 'dataId',
  },
  {
    type: 'custom',
    name: 'flowList',
    label: '审批人',
    rules: [
      {
        validator: (rule: any, value: any, callback: any) => {
          if (value?.length && value.some((e: any) => !e.approveAccountId)) {
            callback(new Error('请选择审批人'))
          } else {
            callback()
          }
        },
      },
    ],
  },
])

const handleUploadSuccess = (r: any, { validate, updateValue }: any, newFileList: any[]) => {
  updateValue(newFileList.map((it: any) => it?.response?.data?.absUrl || it.name))
  validate()
}

const getUserOptions = () =>
  UserApi.getUserAllList().then((res) =>
    res.data.map((it: any) => ({
      label: it.accountName,
      value: it.accountId,
    })),
  )

const mapRef = ref<InstanceType<typeof TMapView> | null>(null)
const markTool = ref<TPointMarkerTool | null>(null)
const markToolActive = ref(false)
// 使用计算属性来访问状态
const isMarkToolActive = computed(() => markToolActive.value)
// 监听标注工具状态
const updateMarkToolState = () => {
  markToolActive.value = markTool.value?.isActivated() ?? false
}

const mapOptions = {
  center: {
    lng: BJ_CENTER_POINT.lng,
    lat: BJ_CENTER_POINT.lat,
  },
  search: {
    show: true,
    type: TSearchType.VIEWPORT,
    placeholder: '搜索地点或地址',
    timeout: 15000,
    maxResults: 20,
  },
}
const onMapReady = (map: TMap) => {
  map.switchBaseLayer('img')
  // 在地图准备好后初始化标注工具
  markTool.value = map.createPointMarkerTool({
    iconName: 'marker-loc',
    enableGeocoding: true,
    autoSetAddress: true,
    updateAddressOnDrag: true,
    geocodeDebounceTime: 0,
    // text: '一个标注 ',
    mode: TPointMarkerMode.FOLLOW | TPointMarkerMode.SINGLE,
    width: 32,
    height: 48,
    textOffset: [10, 0],
    svgStyle: {
      fill: 'red',
    },
    onMark: () => {
      map.clearOverlays()
      updateMarkToolState()
    },
    onGeocodeComplete(marker, address, detail) {
      // form.questionLocation = marker.getPosition().toString()
      const values = formRef.value?.getValues()
      Object.assign(form, {
        ...form,
        ...values,
        questionLocation: marker.getPosition().toString(),
        questionLocationName: address,
      })
      // form.questionLocationName = address
    },
  })
}
// 在操作标注工具时更新状态
const toggleMarkTool = () => {
  if (markTool.value) {
    const result = markTool.value.toggle()
    markToolActive.value = result
    if (result) {
      ElMessage.info('请点击地图添加标注')
    }
  }
}

const isSubmitting = ref(false)
const isExamine = ref(false)

const submit = (type: string) => {
  formRef.value.validate(async (valid: boolean, formValues: any) => {
    if (valid) {
      try {
        if (type === '0') {
          isSubmitting.value = true
        } else {
          isExamine.value = true
        }
        const params = {
          ...formValues,
          approveStatus: type,
          questionImg: formValues.questionImg?.toString(),
          questionFile: formValues.questionFile?.toString(),
          startDate: dayjs(formValues.startDate).format('YYYY-MM-DD'),
          endDate: dayjs(formValues.endDate).format('YYYY-MM-DD'),
        }
        if (route.query.id) {
          await SupervisionApi.updateCheck(route.query.id as string, params)
        } else {
          await SupervisionApi.addCheck(params)
        }
        isSubmitting.value = false
        ElMessage.success(t('message.saveSuccess'))
        goBack()
      } catch (err: any) {
        isSubmitting.value = false
        ElMessage.error(t('message.saveFailed'))
        console.error(err)
      }
    }
  })
}
const getInfo = async () => {
  const { data } = await SupervisionApi.getCheckById(route.query.id as string)
  Object.assign(form, {
    ...data,
    dutyDeptId: `${data.dutyDeptId}`,
    superviseDeptId: `${data.superviseDeptId}`,
    questionImg: data.questionImg?.split(','),
    questionFile: data.questionFile?.split(','),
  })
  const map = mapRef.value?.getMap()
  if (!map) return
  if (data.questionLocation) {
    const [lng, lat] = data.questionLocation.split(',')
    const circleSvg = new TSvgOverlay({
      position: [lng, lat],
      text: data.questionLocationName,
      iconName: `marker-loc`,
      svgStyle: {
        fill: 'red',
      },
      size: [35, 50],
      anchor: TAnchorPosition.BOTTOM_CENTER,
    })
    map.addOverlay(circleSvg)
    map.setCenter(
      {
        lng,
        lat,
      },
      15,
    )
  }
}
onMounted(() => {
  if (route.query.id) {
    getInfo()
  }
})
</script>

<style scoped lang="scss"></style>
