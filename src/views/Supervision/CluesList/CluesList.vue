<template>
  <z-page class="pt-2">
    <el-tabs
      v-model="tabsActive"
      @tab-click="tabsClick"
    >
      <el-tab-pane
        v-for="v in tabsRows"
        :key="v.value"
        :label="v.label"
        :name="v.value"
      ></el-tab-pane>
    </el-tabs>
    <z-table
      row-key="recordId"
      ref="tableRef"
      v-model:query="query"
      :columns="columns"
      :fetch-data="fetchTableData"
      paginationMode="server"
    >
      <template #beforePicPath="{ row }">
        <el-image :src="ASSETS_URL + row.beforePicPath" preview-teleported :preview-src-list="[ASSETS_URL + row.beforePicPath]" class="w-[80px] h-[80px]" />
      </template>
      <template #endPicPath="{ row }">
        <el-image :src="ASSETS_URL + row.endPicPath" preview-teleported :preview-src-list="[ASSETS_URL + row.endPicPath]" class="w-[80px] h-[80px]" />
      </template>
      <template #operation="{ row }">
        <z-dynamic-route
          :route="{
            path: '/CheckDetails',
            viewPath: '@/views/Supervision/CheckList/CheckDetails.vue',
            meta: {
              title: '查看督办清单'
            }
          }"
        >
          <template #default="{ navigate }">
            <el-button
              size="small"
              type="primary"
              link
              @click="() => navigate({
                query: {
                  id: row.recordId
                }
              })"
            >{{ t('common.view') }}
            </el-button>
          </template>
        </z-dynamic-route>
      </template>
    </z-table>
  </z-page>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import type { ZTableColumn, ZTableParams } from '@/ui/components/ZTable/types.ts'
import { useTypedI18n } from '@/i18n'
import dataResApi from '@/api/dataResources.ts'
import DictApI from '@/api/dict.ts'
import ComApi from '@/api/common.ts'

const { t } = useTypedI18n()

const ASSETS_URL = import.meta.env.VITE_STATIC_ASSETS_URL

const tableRef = ref()
const tabsActive = ref('2')
const tabsRows = ref([
  { value: '1', label: '视频监控' },
  { value: '2', label: '遥感监测' },
  { value: '3', label: '无人机查询' }
])
const tabsClick = () => {
  tableRef.value?.reset()
}

const query = ref()
const columns = ref<ZTableColumn[]>([
  {
    prop: 'dataType',
    label: '图斑类型',
    asyncFormatter: {
      cacheKey: 'anyQuestionTypeValue',
      async fetch() {
        const { data } = await DictApI.getDictItemsByCode('questiontypes')
        return data
      },
      render: (value) => (row) => value.find((it: any) => it.dictionaryCode === row.dataType)?.dictionaryName,
    },
  },
  { prop: 'geom', label: '位置', showOverflowTooltip: true },
  {
    prop: 'regionCode',
    label: '所属行政区县',
    asyncFormatter: {
      cacheKey: 'regionCodeValue',
      async fetch() {
        return await ComApi.getAreaList().then(res => res.data)
      },
      render: (value) => (row) => value.find((it: any) => it.areaCode === row.regionCode)?.areaName,
    },
  },
  { prop: 'area', label: '图斑面积' },
  { prop: 'beforePicTime', label: '前时相时间' },
  { prop: 'beforePicPath', label: '前时相影像' },
  { prop: 'endPicTime', label: '后时相时间' },
  { prop: 'endPicPath', label: '后时相影像' },
  { prop: 'operation', label: '操作', align: 'center', width: 180, fixed: 'right' },
])

const fetchTableData = async (params: ZTableParams) => {
  return dataResApi.getRemoteDataList({ ...params, queryType: tabsActive.value }).then(
    (res) => res.data,
  )
}
</script>

<style scoped lang="scss"></style>
