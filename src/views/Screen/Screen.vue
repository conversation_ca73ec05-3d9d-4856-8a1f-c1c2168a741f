<template>
  <z-screen-container :loading="loading">
    <div
      class="h-[149px] absolute left-0 right-0 top-0 bg-[url(@/assets/imgs/screen/topbg-1.png)] bg-[length:100%_100%] z-3"
    >
      <div class="flex justify-between px-[40px] pt-[13px] relative">
        <img src="@/assets/imgs/screen/back.png" alt="" class="w-[40px] h-[40px] absolute left-[28px] top-[25px] z-2 cursor-pointer" @click="router.back()">
        <div class="flex gap-x-4 ml-[34px]">
          <div
            v-for="v in tabsLeftRows"
            :key="v.thematicId"
            class="h-[56px] rounded-[10px] flex justify-center items-center cursor-pointer px-[30px] skew-x-20 tabs-item"
            :class="{ active: v.thematicId === tabActive }"
            @click="onTab(v)"
          >
          <span class="text-[26px] c-white skew-x--20 youshe-bold select-none">{{
              v.thematicName
            }}</span>
          </div>
        </div>
        <div class="flex gap-x-4">
          <div
            v-for="v in tabsRightRows"
            :key="v.thematicId"
            class="h-[56px] rounded-[10px] flex justify-center items-center cursor-pointer px-[35px] skew-x--20 tabs-item"
            :class="{ active: v.thematicId === tabActive }"
            @click="onTab(v)"
          >
          <span class="text-[26px] c-white skew-x-20 youshe-bold select-none">{{
              v.thematicName
            }}</span>
          </div>
        </div>
      </div>
    </div>
    <component :is="curModule" :key="tabActive" />
  </z-screen-container>
  <ControlPanel />
</template>

<script setup lang="ts">
import { useBus } from '@/composables/useBus.ts'
import { computed, provide, ref, onMounted } from 'vue'
import { loadModuleComponents, type ModuleComponent } from './utils/loader.ts'
import type { DefineComponent } from 'vue'
import ScreenApi from '@/api/screen.ts'
import { ScreenStateKey } from './utils/injection.ts';
import { cacheService } from '@/utils/cache.ts'
import { SCREEN_MENU_ID_KEY } from './utils/conts.ts'
import ControlPanel from './modules/VideoMonitorModule/module/ControlPanel.vue'
import type { MonitoringGrid } from './modules/VideoMonitorModule/types.ts'
import { useRouter } from 'vue-router'

const router = useRouter()
const emitter = useBus()

const selectedGrid = ref<MonitoringGrid | null>(null)

provide('SELECT_GRID', selectedGrid)

let modules: ModuleComponent[] = []
const loadedModules = async () => {
  modules = await loadModuleComponents()
}
loadedModules()

const tabsLeftRows = computed<any[]>(() => featuredTree.value.slice(0, 3))
const tabsRightRows = computed<any[]>(() => featuredTree.value.slice(3, 6))

const loading = ref<boolean>(true)
const tabActive = ref()
const curModule = computed<DefineComponent >(() => {
  const curItem = featuredTree.value?.find((e: any) => e.thematicId === tabActive.value)
  return modules.find((it: ModuleComponent) => it.name === curItem?.thematicRoute)?.component
})

const onTab= (v: any) => {
  tabActive.value = v.thematicId
  cacheService.set(SCREEN_MENU_ID_KEY, v.thematicId, { strategy: 'session' })
}

const featuredTree = ref<any[]>([])
const getThematicFeatureTree = async () => {
  try {
    loading.value = true
    const { data } = await ScreenApi.getThematicFeatureTree()
    featuredTree.value = data
    const thematicId = cacheService.get(SCREEN_MENU_ID_KEY, { strategy: 'session' })
    tabActive.value = thematicId || data[0]?.thematicId
    loading.value = false
  } catch {}
}
getThematicFeatureTree()

provide(ScreenStateKey, {
  tId: tabActive,
  featuredTree
})

onMounted(() => {
  emitter.on('SETCMAERAINFO', (data: MonitoringGrid) => {
    selectedGrid.value = data
  })
})
</script>

<style scoped lang="scss">
.tabs-item {
  background: linear-gradient(180deg, rgba(32, 115, 191, 0.13) 0%, #1a65ab 100%);
  &.active {
    background: linear-gradient(180deg, rgba(0, 126, 242, 0.16) 0%, #007ef2 100%);
  }
}
</style>
