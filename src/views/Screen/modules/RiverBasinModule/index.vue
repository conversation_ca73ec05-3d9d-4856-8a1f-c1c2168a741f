<template>
  <div class="absolute h-full w-full">
    <TMapView
      class="z-1"
      ref="mapRef"
      :options="mapOptions"
      @ready="onMapReady"
      scale-screen-selector=".screen-wrapper"
    />
    <ColTabs
      ref="tabsRef"
      @ready="onTabsReady"
    />
    <Search
      :map-data="mapData"
      @search="onSearch"
    />
    <FeatureLayer />
    <Toolbar @map-event="mapEvent" />
    <Details ref="detailsRef" />
  </div>
</template>

<script setup lang="ts">
import { TAnchorPosition, TMap, TMapView, TSvgOverlay, TPolygonTool, TPolylineTool } from '@/map'
import { ref } from 'vue'
import ScreenApi from '@/api/screen.ts'
import { ElLoading, ElMessage } from 'element-plus'
import Toolbar from './components/Toolbar.vue'
import Search from './components/Search.vue'
import ColTabs from './components/ColTabs.vue'
import Details from './components/Details.vue'
import FeatureLayer from './components/FeatureLayer.vue'
import { MAP_POLYGON_COLOR, MAP_POLYLINE_COLOR } from '@/views/Screen/utils/conts.ts'
import { BJ_CENTER_POINT } from '@/config/default-settings.ts'

const polygonTool = ref<TPolygonTool | null>(null)
const polylineTool = ref<TPolylineTool | null>(null)

const tabsRef = ref<InstanceType<typeof ColTabs> | null>(null)
const onTabsReady = (ids: number[]) => {
  getMapData(ids)
}

const detailsRef = ref<InstanceType<typeof Details> | null>(null)
const mapRef = ref<InstanceType<typeof TMapView> | null>(null)

const mapOptions = {
  center: {
    lng: BJ_CENTER_POINT.lng,
    lat: BJ_CENTER_POINT.lat,
  },
  search: {
    show: false,
  },
}
// 多边形工具控制
const togglePolygonTool = () => {
  console.log(polygonTool.value, '////多边形工具控制')
  if (!polygonTool.value) return
  // polygonTool.value.stop()
  polygonTool.value.start()
}

// 折线工具控制
const togglePolylineTool = () => {
  if (!polylineTool.value) return
  //  polylineTool.value.stop()
  console.log(polylineTool.value.start, '////折线工具控制')
  polylineTool.value.start()
}
const onMapReady = (map: TMap) => {
  // 设置初始底图类型为影像图
  map.switchBaseLayer('img')
  // 初始化多边形工具
  polygonTool.value = map.createPolygonTool({
    color: '#1890ff',
    weight: 2,
    opacity: 1,
    fillColor: '#1890ff',
    fillOpacity: 0.3,
    lineStyle: 'solid',
    showArea: true,
  })

  // 初始化折线工具
  polylineTool.value = map.createPolylineTool({
    color: '#ff4d4f',
    weight: 3,
    opacity: 1,
    lineStyle: 'solid',
    showDistance: true,
  })
  // 加载分类树
  tabsRef.value?.loadTree()
}

const onSearch = (id: number) => {
  const map = mapRef.value?.getMap()
  if (!map) return
  const { location, locationType } = mapData.value.find((e) => e.thematicFeatureDataId === id)
  if (!location) {
    ElMessage.warning('此数据未配置坐标值')
    return
  }
  const coordinates = JSON.parse(location)
  const [lng, lat] = locationType === '1' ? coordinates : coordinates[0]
  map.setCenter(
    {
      lng,
      lat,
    },
    15,
  )
}

const mapEvent = (data: any) => {
  const { event } = data
  if (event === 'zoomIn') {
    mapRef.value?.zoomIn()
  }
  if (event === 'zoomOut') {
    mapRef.value?.zoomOut()
  }
  if (event === 'resetView') {
    mapRef.value?.resetView()
  }
  if (event === 'ranging') {
    console.log('测距')
    togglePolylineTool()
  }
  if (event === 'measuringSurface') {
    console.log('测面')
    togglePolygonTool()
  }
}

const drawPoint = (list: any[], map: TMap) => {
  if (!list?.length) return
  list.forEach((item) => {
    if (item.location) {
      const [lng, lat] = JSON.parse(item.location)
      const circleSvg = new TSvgOverlay({
        position: [lng, lat],
        iconName: `marker-${item.thematicClassIcon}`,
        size: [35, 50],
        anchor: TAnchorPosition.BOTTOM_CENTER,
        events: {
          click: () => {
            detailsRef.value?.open(item)
          },
        },
      })
      map.addOverlay(circleSvg)
    }
  })
}

const drawLine = (list: any[], map: TMap) => {
  if (!list?.length) return
  list.forEach((item) => {
    if (item.location) {
      const location = JSON.parse(item.location)
      map.createPolyline({
        path: location,
        color: MAP_POLYLINE_COLOR.color,
        weight: 3,
        opacity: 1,
        events: {
          click: () => {
            detailsRef.value?.open(item)
          },
        },
      })
    }
  })
}

const drawPolygon = (list: any[], map: TMap) => {
  if (!list?.length) return
  list.forEach((item) => {
    if (item.location) {
      const location = JSON.parse(item.location)
      map.createPolygon({
        path: location,
        color: MAP_POLYGON_COLOR.borderColor,
        weight: 2,
        opacity: 1,
        fillColor: MAP_POLYGON_COLOR.fillColor,
        fillOpacity: 0.7,
        events: {
          click: () => {
            detailsRef.value?.open(item)
          },
        },
      })
    }
  })
}

const draw = () => {
  const data = mapData.value
  const map = mapRef.value?.getMap()
  if (!map) return
  map.clearOverlays()
  drawPoint(
    data.filter((e: any) => e.locationType === '1'),
    map,
  )
  drawLine(
    data.filter((e: any) => e.locationType === '2'),
    map,
  )
  drawPolygon(
    data.filter((e: any) => e.locationType === '3'),
    map,
  )
}

const mapData = ref<any[]>([])
const getMapData = (ids: number[]) => {
  if (!ids?.length) {
    const map = mapRef.value?.getMap()
    if (!map) return
    map.clearOverlays()
    return
  }
  const loading = ElLoading.service({
    lock: true,
    text: '加载中...',
    background: 'rgba(0, 0, 0, 0.7)',
  })
  ScreenApi.getThematicFeatureDataList({
    thematicClassIds: ids?.toString(),
  })
    .then((res) => {
      mapData.value = res?.data || []
      draw()
    })
    .finally(() => {
      loading.close()
    })
}
</script>

<style scoped lang="scss"></style>
