// 模块枚举
export enum ModuleType {
  RiverBasin = 'RiverBasinModule',
  FeaturedProducts = 'FeaturedProductsModule',
  IndustryChain = 'IndustryChainModule',
  KeyProject = 'KeyProjectModule',
  VideoMonitor = 'VideoMonitorModule'
}
interface ModuleOptions {
  value: ModuleType
  label: string
}
export const moduleTypeOptions: ModuleOptions[] = [
  {
    value: ModuleType.RiverBasin,
    label: '流域一张图'
  },
  {
    value: ModuleType.FeaturedProducts,
    label: '特色产品'
  },
  {
    value: ModuleType.IndustryChain,
    label: '产业链'
  },
  {
    value: ModuleType.KeyProject,
    label: '重点项目'
  },
  {
    value: ModuleType.VideoMonitor,
    label: '视频监控'
  }
]

// 图表枚举
export enum ChartType {
  List = 'List',
  Line = 'Line',
  Pie = 'Pie',
  Count = 'Count'
}
interface ChartOptions {
  value: ChartType
  label: string
}
export const chartTypeOptions: ChartOptions[] = [
  {
    value: ChartType.List,
    label: '列表'
  },
  {
    value: ChartType.Line,
    label: '折线图'
  },
  {
    value: ChartType.Pie,
    label: '饼图'
  },
  {
    value: ChartType.Count,
    label: '统计'
  }
]
