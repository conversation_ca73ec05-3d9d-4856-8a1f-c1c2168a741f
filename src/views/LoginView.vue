<template>
  <div class="login">
    <div class="login__container">
      <div class="login__header">
        <img src="@/assets/logo.png" alt="Logo" class="login__logo">
        <h1 class="login__title">{{ appTitle }}</h1>
      </div>

      <el-form ref="formRef" :model="formData" :rules="rules" class="login__form" size="large"
        @keyup.enter="handleSubmit">
        <el-form-item prop="accountNumber">
          <el-input v-model="formData.accountNumber" :prefix-icon="User" placeholder="请输入账号" />
        </el-form-item>

        <el-form-item prop="accountPassword">
          <el-input v-model="formData.accountPassword" :prefix-icon="Lock" type="password" placeholder="请输入密码"
            show-password />
        </el-form-item>

        <el-form-item prop="verifyCode">
          <div class="login__verify-code">
            <el-input v-model="formData.verifyCode" :prefix-icon="Key" placeholder="请输入验证码" />
            <img :src="verifyCodeBase64" alt="验证码" class="login__verify-code-img" @click="refreshVerifyCode">
          </div>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" class="login__submit" :loading="loading" @click="handleSubmit">
            登录
          </el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { User, Lock, Key } from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'
import { useUserStore } from '@/stores/user'
import AuthApi from '@/api/auth'
import uniqueId from 'lodash/uniqueId'
import { ElMessage } from 'element-plus'

const appTitle = import.meta.env.VITE_APP_TITLE
const router = useRouter()
const route = useRoute()
const { login } = useUserStore()

const formRef = ref<FormInstance>()
const loading = ref(false)
const verifyCodeBase64 = ref<string>()

const formData = reactive({
  accountNumber: '',
  accountPassword: '',
  verifyCode: '',
  uniqueIdentifier: '',
  loginType: 'ACCOUNT_PASSWORD'
})

const rules: FormRules = {
  accountNumber: [
    { required: true, message: '请输入账号', trigger: 'blur' },
    { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  accountPassword: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  verifyCode: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { len: 4, message: '验证码长度为 4 位', trigger: 'blur' }
  ]
}

const refreshVerifyCode = async () => {
  const uuid = uniqueId()
  try {
    const res = await AuthApi.getVerifyCode(uuid)
    formData.uniqueIdentifier = uuid
    verifyCodeBase64.value = res.data
  } catch {}
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    formRef.value.validate((isVaid => {
      if (isVaid) {
        loading.value = true
        login(formData).then(()=>{
          router.push({
          path: route.query.redirect?.toString() ?? '/',
          replace: true
        })
        })
          .catch(() => {
            refreshVerifyCode()
          })
          .finally(() => {
          loading.value = false
        })
      }
    }))
  } catch {
    ElMessage.error('登录失败，请检查账号密码')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  refreshVerifyCode()
})
</script>

<style lang="scss" scoped>
.login {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 1rem;

  &__container {
    background: var(--el-bg-color);
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 400px;
  }

  &__header {
    text-align: center;
    margin-bottom: 2rem;
  }

  &__logo {
    width: 64px;
    height: 64px;
    margin-bottom: 1rem;
  }

  &__title {
    font-size: 1.5rem;
    color: var(--el-text-color-primary);
    margin: 0;
  }

  &__form {
    :deep(.el-input__wrapper) {
      box-shadow: 0 0 0 1px var(--el-border-color) inset;
    }
  }

  &__verify-code {
    display: flex;
    gap: 1rem;

    :deep(.el-input) {
      flex: 1;
    }
  }

  &__verify-code-img {
    height: 40px;
    border-radius: 4px;
    cursor: pointer;
  }

  &__submit {
    width: 100%;
  }
}
</style>
