<template>
  <div class="z-upload">
    <el-upload
      ref="uploadRef"
      v-model:file-list="fileList"
      :action="uploadUrl"
      :headers="headers"
      :accept="acceptTypes"
      :limit="limit"
      :multiple="multiple"
      :show-file-list="showFileList"
      :http-request="customUpload || undefined"
      :before-upload="handleBeforeUpload"
      :before-remove="handleBeforeRemoveFromUpload"
      :on-success="handleSuccess"
      :on-error="handleError"
      :on-exceed="handleExceed"
      :on-progress="handleProgress"
      :on-remove="handleRemoveFromUpload"
      drag
    >
      <slot name="trigger">
        <el-icon class="z-upload__icon">
          <Upload />
        </el-icon>
        <div class="z-upload__text">将文件拖到此处，或 <em>点击上传</em></div>
      </slot>
      <template #tip>
        <div class="z-upload__tip">
          <slot name="tip">{{ tip || generateDefaultTip }}</slot>
        </div>
      </template>
    </el-upload>

    <el-progress
      v-if="uploadProgress > 0 && uploadProgress < 100"
      :percentage="uploadProgress"
      class="z-upload__progress"
    />

    <div
      v-if="showPreview && previewFiles.length > 0"
      class="z-upload__preview-list"
    >
      <preview-item
        v-for="(file, index) in previewFiles"
        :key="file.uid"
        :file="file"
        :preview-url="getPreviewUrl(file)"
        v-bind="finalPreviewItemProps"
        @remove="handleRemove(file, index)"
        @preview="handlePreview(file)"
      />
    </div>

    <el-dialog
      v-model="previewVisible"
      :title="previewFile?.name"
      :append-to-body="true"
      class="z-upload__preview-dialog"
      width="800px"
      :close-on-click-modal="false"
    >
      <div class="z-upload__preview-dialog-content">
        <component
          :is="getPreviewComponent(previewFile)"
          v-if="previewFile"
          :src="getPreviewUrl(previewFile)"
          :style="getPreviewStyle(previewFile)"
          controls
        />
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onBeforeUnmount, onMounted, watch } from 'vue'
import {
  ElMessage,
  ElMessageBox,
  ElUpload,
  type UploadFile,
  type UploadProps,
  type UploadRawFile,
} from 'element-plus'
import { Upload } from '@element-plus/icons-vue'
import { getAuthHeaders } from '@/utils/auth'
import {
  MIME_TYPE_MAP,
  FILE_EXTENSION_MAP,
  DEFAULT_ALLOWED_TYPES,
  DEFAULT_UPLOAD_LIMIT,
  DEFAULT_MAX_SIZE,
  MESSAGES,
  ViewType,
  FileType,
} from './constants'
import PreviewItem from './components/PreviewItem.vue'
import { createUploadFiles } from './utils'

interface FileSource {
  id?: string | number
  url?: string
  name?: string
  status?: 'success' | 'uploading' | 'fail'
  response?: any
}

interface UploadFileProps {
  uploadUrl?: string
  accept?: string
  allowedTypes?: FileType[]
  fileTypeMap?: Partial<Record<FileType, string[]>>
  limit?: number
  multiple?: boolean
  tip?: string
  headers?: Record<string, string>
  maxSize?: number
  customUpload?: UploadProps['httpRequest']
  beforeSuccess?: (response: any, file: UploadFile, fileList: UploadFile[]) => UploadFile[] | false
  beforeRemove?: (file: UploadFile, fileList: UploadFile[]) => boolean | Promise<boolean>
  onRemove?: UploadProps['onRemove']
  previewFilter?: (file: UploadFile) => boolean
  showPreview?: boolean
  showMessage?: boolean
  modelValue?: FileSource | string | (FileSource | string)[] | null
  showFileList?: boolean
  showFileInfo?: boolean
  showRemoveConfirm?: boolean
  previewItemProps?: {
    width?: number
    height?: number
    infoConfig?: {
      showName?: boolean
      showSize?: boolean
      nameMaxLength?: number
      showType?: boolean
      showTime?: boolean
    }
  }
}

const generateDefaultTip = computed(() => {
  const extensions = props.allowedTypes
    .flatMap((type) => fileTypeMap.value[type] || [])
    .map((ext) => `.${ext}`)
    .join('、')
  return `支持${extensions}，单个文件不超过${props.maxSize}MB`
})

const props = withDefaults(defineProps<UploadFileProps>(), {
  uploadUrl: import.meta.env.VITE_API_UPLOAD_URL,
  accept: '',
  allowedTypes: () => DEFAULT_ALLOWED_TYPES,
  fileTypeMap: () => FILE_EXTENSION_MAP,
  limit: DEFAULT_UPLOAD_LIMIT,
  multiple: false,
  headers: () => getAuthHeaders(),
  maxSize: DEFAULT_MAX_SIZE,
  previewFilter: () => (file: UploadFile) => file.status === 'success',
  showPreview: true,
  showMessage: true,
  showFileList: true,
  modelValue: null,
  showRemoveConfirm: false,
  beforeSuccess: (response: any, _file: UploadFile, fileList: UploadFile[]) => {
    if (response.result !== 0) {
      throw new Error(MESSAGES.SERVER_ERROR(response.result))
    }
    return fileList
  },
  beforeRemove: () => true,
  previewItemProps: () => ({
    width: 120,
    height: 120,
    infoConfig: {
      showName: true,
      showSize: true,
      nameMaxLength: 20,
      showType: false,
      showTime: false,
    },
  }),
})

const finalPreviewItemProps = computed(() => {
  if (!props.showFileInfo) {
    return {
      ...props.previewItemProps,
      infoConfig: {
        showName: false,
        showSize: false,
        showType: false,
        showTime: false,
      },
    }
  }
  return props.previewItemProps
})

const emit = defineEmits<{
  (e: 'update:modelValue', value: UploadFile[]): void
  (e: 'update:fileList', value: UploadFile[]): void
  (e: 'success', response: any, file: UploadFile, fileList: UploadFile[]): void
  (e: 'error', error: any, file: UploadFile, fileList: UploadFile[]): void
  (e: 'progress', percent: number): void
  (e: 'remove', file: UploadFile, index: number): void
  (e: 'exceed', files: File[], fileList: UploadFile[]): void
}>()

const uploadRef = ref<InstanceType<typeof ElUpload>>()

const fileList = ref<UploadFile[]>(
  createUploadFiles(Array.isArray(props.modelValue) ? props.modelValue : [props.modelValue]),
)

watch(
  () => props.modelValue,
  (newValue) => {
    fileList.value = createUploadFiles(Array.isArray(newValue) ? newValue : [newValue])
  },
)

const uploadProgress = ref(0)

const fileTypeMap = computed(() => ({
  ...FILE_EXTENSION_MAP,
  ...props.fileTypeMap,
}))

const acceptTypes = computed(() => {
  if (props.accept) return props.accept
  const extensions = props.allowedTypes
    .flatMap((type) => fileTypeMap.value[type] || [])
    .map((ext) => `.${ext}`)
  return extensions.join(',')
})

const previewFiles = computed(() => fileList.value.filter(props.previewFilter))

const getFileExtension = (file: UploadRawFile): string => {
  const parts = file.name.split('.')
  return parts.length > 1 ? parts.pop()?.toLowerCase() || '' : ''
}

const validateFileType = (file: UploadRawFile): boolean => {
  debugger
  const extension = getFileExtension(file)
  const mimeType = file.type

  const allowedExtensions = props.allowedTypes.flatMap((type) => fileTypeMap.value[type] || [])
  const isExtensionValid = allowedExtensions.includes(extension)

  const allowedMimeTypes = props.allowedTypes.flatMap((type) => MIME_TYPE_MAP[type] || [])
  const isMimeTypeValid = allowedMimeTypes.length === 0 || allowedMimeTypes.includes(mimeType)

  if (!isExtensionValid || !isMimeTypeValid) {
    const allowedTypesStr = props.allowedTypes
      .map((type) => `${type} (${fileTypeMap.value[type]?.join(', ') || '无'})`)
      .join(', ')
    if (props.showMessage)
      ElMessage.error(`${MESSAGES.FILE_TYPE_ERROR}，支持的文件类型：${allowedTypesStr}`)
    return false
  }
  return true
}

const getPreviewComponent = (file: UploadFile): string => {
  const extension = file.raw
    ? getFileExtension(file.raw as UploadRawFile)
    : file.name.split('.').pop()?.toLowerCase() || ''

  if (fileTypeMap.value.image?.includes(extension)) {
    return 'img'
  } else if (fileTypeMap.value.video?.includes(extension)) {
    return ViewType.VIDEO
  } else if (fileTypeMap.value.audio?.includes(extension)) {
    return ViewType.AUDIO
  } else if (extension === 'pdf') {
    return ViewType.PDF
  } else {
    return ViewType.FILE
  }
}

const getPreviewStyle = computed(() => (file: UploadFile | null) => {
  if (!file) return {}

  const component = getPreviewComponent(file)

  // 对于 iframe (PDF) 类型，使用具体像素值
  if (component === ViewType.PDF) {
    const viewportWidth = windowSize.value.width
    const viewportHeight = windowSize.value.height

    // 移动端和桌面端使用不同的计算策略
    const isMobile = viewportWidth <= 768

    if (isMobile) {
      // 移动端：使用更大的屏幕占比
      const dialogPadding = 10
      const headerHeight = 50
      const maxWidth = viewportWidth * 0.95 - dialogPadding * 2
      const maxHeight = viewportHeight * 0.8 - headerHeight - dialogPadding * 2

      return {
        width: `${maxWidth}px`,
        height: `${maxHeight}px`,
        border: 'none',
        borderRadius: '4px',
      }
    } else {
      // 桌面端：计算 iframe 的实际尺寸，需要减去对话框的边距和头部高度
      const dialogPadding = 20
      const headerHeight = 60
      const maxWidth = Math.min(viewportWidth * 0.85, 1400) - dialogPadding * 2
      const maxHeight = Math.min(viewportHeight * 0.85, 900) - headerHeight - dialogPadding * 2

      return {
        width: `${maxWidth}px`,
        height: `${maxHeight}px`,
        border: 'none',
        borderRadius: '4px',
      }
    }
  }

  // 对于图片和视频，自适应对话框大小
  if (component === ViewType.IMAGE || component === ViewType.VIDEO) {
    return {
      maxWidth: '100%',
      maxHeight: '100%',
      objectFit: 'contain',
    }
  }

  // 对于音频，设置合适的宽度
  if (component === ViewType.AUDIO) {
    return {
      width: '100%',
      maxWidth: '500px',
    }
  }

  return {}
})

type CacheKey = string

const generateCacheKey = (file: UploadFile): CacheKey => {
  return `${file.uid || file.name}`
}

const previewUrlMap = new Map<string, string>()

const getPreviewUrl = (file: UploadFile): string => {
  if (file.url) return file.url

  if (file.raw) {
    const cacheKey = generateCacheKey(file)
    let url = previewUrlMap.get(cacheKey)

    if (!url) {
      url = URL.createObjectURL(file.raw)
      previewUrlMap.set(cacheKey, url)
    }

    return url
  }

  return ''
}

const clearPreviewUrl = (file: UploadFile) => {
  const cacheKey = generateCacheKey(file)
  const url = previewUrlMap.get(cacheKey)
  if (url) {
    URL.revokeObjectURL(url)
    previewUrlMap.delete(cacheKey)
  }
}

const handleBeforeUpload = (file: UploadRawFile): boolean => {
  const isLtMaxSize = file.size / 1024 / 1024 < props.maxSize
  if (!isLtMaxSize) {
    if (props.showMessage) ElMessage.error(MESSAGES.FILE_SIZE_EXCEED(props.maxSize))
    return false
  }
  return validateFileType(file)
}

// 处理 el-upload 的 before-remove 事件
const handleBeforeRemoveFromUpload = async (file: UploadFile): Promise<boolean> => {
  if (props.beforeRemove) {
    try {
      const result = await props.beforeRemove(file, fileList.value)
      return result !== false
    } catch (error) {
      if (props.showMessage) {
        ElMessage.error('删除失败: ' + (error as Error).message)
      }
      return false
    }
  }
  return true
}

const handleRemoveFromUpload: UploadProps['onRemove'] = (file, files) => {
  clearPreviewUrl(file)

  const index = fileList.value.findIndex((item) => item.uid === file.uid)

  fileList.value = files

  if (props.onRemove) {
    props.onRemove(file, files)
  }

  emit('update:modelValue', files)
  emit('update:fileList', files)
  emit('remove', file, index)

  if (props.showMessage) {
    ElMessage.success('文件删除成功')
  }
}

const handleSuccess: UploadProps['onSuccess'] = (response, file, files) => {
  let newFileList = [...files]
  if (props.beforeSuccess) {
    try {
      const result = props.beforeSuccess(response, file, newFileList)
      if (result === false) return
      if (Array.isArray(result)) newFileList = result
    } catch (error) {
      handleError(error as Error, file, newFileList)
      return
    }
  }

  uploadProgress.value = 0
  if (props.showMessage) ElMessage.success(MESSAGES.UPLOAD_SUCCESS)
  fileList.value = newFileList
  emit('update:modelValue', newFileList)
  emit('update:fileList', newFileList)
  emit('success', response, file, newFileList)
}

const handleError: UploadProps['onError'] = (error, file, files) => {
  uploadProgress.value = 0
  if (props.showMessage) ElMessage.error(error.message || MESSAGES.UPLOAD_FAILED)
  emit('error', error, file, files)
}

const handleProgress: UploadProps['onProgress'] = (event) => {
  if (!props.customUpload) {
    uploadProgress.value = Math.round(event.percent)
    emit('progress', event.percent)
  }
}

const handleExceed: UploadProps['onExceed'] = (files, uploadFiles) => {
  if (props.showMessage) ElMessage.warning(MESSAGES.EXCEED_LIMIT(props.limit))
  emit('exceed', files, uploadFiles as UploadFile[])
}

const handleRemove = async (file: UploadFile, index: number) => {
  if (props.showRemoveConfirm) {
    try {
      await ElMessageBox.confirm(`确定要删除文件 "${file.name}" 吗？`, '删除确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
    } catch {
      return
    }
  }

  if (uploadRef.value) {
    uploadRef.value.handleRemove(file)
  } else {
    const canRemove = await handleBeforeRemoveFromUpload(file)
    if (canRemove) {
      const newFileList = fileList.value.filter((item) => item.uid !== file.uid)
      clearPreviewUrl(file)
      fileList.value = newFileList

      if (props.onRemove) {
        props.onRemove(file, newFileList)
      }

      emit('update:modelValue', newFileList)
      emit('update:fileList', newFileList)
      emit('remove', file, index)

      if (props.showMessage) {
        ElMessage.success('文件删除成功')
      }
    }
  }
}

// 响应式窗口大小变化
const windowSize = ref({ width: window.innerWidth, height: window.innerHeight })

const handleResize = () => {
  windowSize.value = { width: window.innerWidth, height: window.innerHeight }
}

onMounted(() => {
  window.addEventListener('resize', handleResize)
})

onBeforeUnmount(() => {
  previewFiles.value.forEach(clearPreviewUrl)
  previewUrlMap.clear()
  window.removeEventListener('resize', handleResize)
})

defineExpose({
  clearFiles: () => uploadRef.value?.clearFiles(),
  submit: () => uploadRef.value?.submit(),
  removeFile: (file: UploadFile) => {
    const index = fileList.value.findIndex((item) => item.uid === file.uid)
    if (index !== -1) {
      handleRemove(file, index)
    }
  },
  removeFileByIndex: (index: number) => {
    const file = fileList.value[index]
    if (file) {
      handleRemove(file, index)
    }
  },
  getFileList: () => fileList.value,
})

const previewVisible = ref(false)
const previewFile = ref<UploadFile | null>(null)

const handlePreview = (file: UploadFile) => {
  previewFile.value = file
  previewVisible.value = true
}
</script>

<style scoped lang="scss">
.z-upload {
  width: 100%;
}

.z-upload__progress {
  margin-top: 10px;
}

.z-upload__preview-list {
  display: flex;
  flex-wrap: wrap;
  margin: 8px -8px;
}

.z-upload__preview-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: var(--preview-item-width, 120px);
}

.z-upload__preview-image,
.z-upload__preview-video,
.z-upload__preview-iframe {
  width: 100px;
  height: 100px;
  object-fit: cover;
  margin-bottom: 5px;
}

.z-upload__preview-audio {
  width: 100px;
  margin-bottom: 5px;
}

.z-upload__preview-name {
  font-size: 12px;
  margin: 5px 0;
  word-break: break-all;
  text-align: center;
}

.z-upload__preview-icon {
  font-size: 32px;
  color: #909399;
}

.z-upload__text {
  color: #606266;
  font-size: 14px;
  text-align: center;
}

.z-upload__text em {
  color: #409eff;
  font-style: normal;
}

.z-upload__tip {
  font-size: 12px;
  color: #909399;
  margin-top: 7px;
}

.z-upload__icon {
  font-size: 28px;
  color: #8c939d;
  margin-bottom: 8px;
}

.z-upload__preview-dialog-content {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  min-height: 400px;
  overflow: auto;
}

.z-upload__preview-dialog-content img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 4px;
}

.z-upload__preview-dialog-content video {
  max-width: 100%;
  max-height: 100%;
  border-radius: 4px;
}

.z-upload__preview-dialog-content audio {
  width: 100%;
  max-width: 500px;
}

.z-upload__preview-dialog-content iframe {
  border: none;
  border-radius: 4px;
  background: #fff;
  /* 宽度和高度由 JavaScript 动态设置 */
}

/* PDF 预览特殊样式 */
.z-upload__preview-dialog .el-dialog__body {
  /* padding: 10px; */
  display: flex;
  flex-direction: column;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .z-upload__preview-dialog-content {
    min-height: 300px;
  }

  .z-upload__preview-dialog-content img,
  .z-upload__preview-dialog-content video {
    max-height: 400px;
  }
}
</style>
