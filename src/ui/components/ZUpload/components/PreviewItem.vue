<template>
  <div
    class="z-upload__preview-item"
    :class="{ 'is-error': error }"
  >
    <div
      class="z-upload__preview-content"
      :style="contentStyle"
    >
      <img
        v-if="previewType === ViewType.IMAGE"
        :src="previewUrl"
        :alt="file.name"
        @load="handlePreviewLoad"
        @error="handlePreviewError"
        class="z-upload__preview-image"
      />

      <video
        v-else-if="previewType === ViewType.VIDEO"
        :src="file.url"
        controls
        @loadeddata="handlePreviewLoad"
        @error="handlePreviewError"
        class="z-upload__preview-video"
      />

      <audio
        v-else-if="previewType === ViewType.AUDIO"
        :src="file.url"
        controls
        @loadeddata="handlePreviewLoad"
        @error="handlePreviewError"
        class="z-upload__preview-audio"
      />

      <iframe
        v-else-if="previewType === ViewType.PDF"
        :src="file.url"
        @load="handlePreviewLoad"
        @error="handlePreviewError"
        class="z-upload__preview-iframe"
      />

      <el-icon
        v-else
        class="z-upload__preview-icon"
      >
        <component :is="fileIcon" />
      </el-icon>

      <div
        v-if="loading"
        class="z-upload__preview-loading"
      >
        <el-icon class="is-loading">
          <Loading />
        </el-icon>
      </div>

      <div
        v-if="error"
        class="z-upload__preview-error"
      >
        <el-icon>
          <Warning />
        </el-icon>
        <span>预览失败</span>
      </div>
    </div>

    <div
      class="z-upload__preview-info"
      v-if="
        infoConfig.showName || infoConfig.showSize || infoConfig.showType || infoConfig.showTime
      "
    >
      <div class="z-upload__preview-info-main">
        <el-tooltip
          v-if="infoConfig.showName"
          :content="file.name"
          placement="top"
          :show-after="1000"
        >
          <span class="z-upload__preview-name">{{ displayFileName }}</span>
        </el-tooltip>
        <span
          v-if="infoConfig.showType"
          class="z-upload__preview-type"
          >{{ fileTypeText }}</span
        >
      </div>
      <div class="z-upload__preview-info-sub">
        <span
          v-if="infoConfig.showSize"
          class="z-upload__preview-size"
          >{{ formatFileSize(file.size) }}</span
        >
        <span
          v-if="infoConfig.showTime"
          class="z-upload__preview-time"
          >{{ uploadTime }}</span
        >
      </div>
    </div>

    <div class="z-upload__preview-actions">
      <el-button
        v-if="canPreview"
        type="primary"
        link
        size="small"
        @click="handlePreview"
      >
        预览
      </el-button>
      <el-button
        type="danger"
        link
        size="small"
        @click="handleRemove"
      >
        删除
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, type ComputedRef } from 'vue'
import type { UploadFile } from 'element-plus'
import { Document, Picture, VideoCamera, Headset, Loading, Warning } from '@element-plus/icons-vue'
import { ViewType } from '../constants'
import { formatFileSize, getPreviewComponentType, isPreviewable } from '../utils'

interface Props {
  file: UploadFile
  previewUrl: string
  width?: number
  height?: number
  infoConfig?: {
    showName?: boolean
    showSize?: boolean
    nameMaxLength?: number
    showType?: boolean
    showTime?: boolean
  }
}

const props = withDefaults(defineProps<Props>(), {
  width: 120,
  height: 120,
  infoConfig: () => ({
    showName: true,
    showSize: true,
    nameMaxLength: 20,
    showType: false,
    showTime: false,
  }),
})

const emit = defineEmits<{
  (e: 'remove'): void
  (e: 'preview'): void
}>()

const loading = ref(true)
const error = ref(false)

const previewType = computed(() => getPreviewComponentType(props.file))

const canPreview: ComputedRef<boolean> = computed(() => isPreviewable(props.file))

const fileIcon = computed(() => {
  const iconMap = {
    [ViewType.IMAGE]: Picture,
    [ViewType.VIDEO]: VideoCamera,
    [ViewType.AUDIO]: Headset,
    [ViewType.FILE]: Document,
  }
  return iconMap[previewType.value as keyof typeof iconMap] || Document
})

const contentStyle = computed(() => ({
  width: `${props.width}px`,
  height: `${props.height}px`,
}))

const handlePreviewLoad = () => {
  loading.value = false
  error.value = false
}

const handlePreviewError = () => {
  loading.value = false
  error.value = true
}

const handleRemove = () => {
  emit('remove')
}

const handlePreview = () => {
  if (canPreview.value) {
    emit('preview')
  }
}

const displayFileName = computed(() => {
  if (!props.infoConfig.showName) return ''
  const name = props.file.name
  if (props.infoConfig.nameMaxLength && name.length > props.infoConfig.nameMaxLength) {
    return name.substring(0, props.infoConfig.nameMaxLength) + '...'
  }
  return name
})

const fileTypeText = computed(() => {
  if (!props.infoConfig.showType) return ''
  return props.file.name.split('.').pop()?.toUpperCase() || ''
})

const uploadTime = computed(() => {
  if (!props.infoConfig.showTime || !props.file.uid) return ''
  return new Date(Number(props.file.uid)).toLocaleString()
})
</script>

<style scoped>
.z-upload__preview-item {
  position: relative;
  display: flex;
  flex-direction: column;
  margin: 8px;
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 8px;
  transition: all 0.3s ease;
  overflow: hidden;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.z-upload__preview-item:hover {
  border-color: var(--el-color-primary);
  transform: translateY(-2px);
  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.1);
}

.z-upload__preview-content {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background-color: var(--el-fill-color-lighter);
  transition: all 0.3s ease;
}

.z-upload__preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.z-upload__preview-item:hover .z-upload__preview-image {
  transform: scale(1.05);
}

.z-upload__preview-video,
.z-upload__preview-audio,
.z-upload__preview-iframe {
  width: 100%;
  height: 100%;
  border: none;
}

.z-upload__preview-icon {
  font-size: 32px;
  color: var(--el-text-color-secondary);
  transition: color 0.3s ease;
}

.z-upload__preview-item:hover .z-upload__preview-icon {
  color: var(--el-color-primary);
}

.z-upload__preview-info {
  padding: 8px 12px;
  width: 100%;
  background: white;
  border-top: 1px solid var(--el-border-color-lighter);
}

.z-upload__preview-info-main {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.z-upload__preview-info-sub {
  display: flex;
  align-items: center;
  gap: 12px;
  color: var(--el-text-color-secondary);
  font-size: 12px;
}

.z-upload__preview-name {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 13px;
  color: var(--el-text-color-primary);
  font-weight: 500;
}

.z-upload__preview-type {
  padding: 2px 6px;
  background: var(--el-fill-color-light);
  border-radius: 4px;
  font-size: 11px;
  color: var(--el-text-color-secondary);
}

.z-upload__preview-size,
.z-upload__preview-time {
  display: inline-flex;
  align-items: center;
}

.z-upload__preview-time {
  font-size: 12px;
}

.z-upload__preview-actions {
  position: absolute;
  top: 0;
  right: 0;
  padding: 8px;
  display: none;
  background: linear-gradient(to left, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.8));
  backdrop-filter: blur(4px);
  border-radius: 0 8px 0 8px;
  box-shadow: -2px 2px 8px rgba(0, 0, 0, 0.1);
}

.z-upload__preview-item:hover .z-upload__preview-actions {
  display: flex;
  gap: 8px;
  animation: fadeIn 0.2s ease;
}

.z-upload__preview-loading,
.z-upload__preview-error {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(4px);
  gap: 8px;
}

.z-upload__preview-error {
  color: var(--el-color-danger);
}

.z-upload__preview-error .el-icon {
  font-size: 24px;
  margin-bottom: 4px;
}

.is-error {
  border-color: var(--el-color-danger);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 暗色模式适配 */
:root[class~='dark'] .z-upload__preview-info {
  background: var(--el-bg-color);
}

:root[class~='dark'] .z-upload__preview-actions {
  background: linear-gradient(to left, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.6));
}
</style>
