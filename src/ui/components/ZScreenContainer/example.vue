<template>
  <div class="screen-container-example">
    <h3>ZScreenContainer 组件使用示例</h3>
    
    <!-- 控制按钮 -->
    <div class="controls">
      <el-button @click="handleManualResize">手动触发 onResize</el-button>
      <el-button @click="handleUpdateScale">手动更新缩放</el-button>
      <el-button @click="handleInitSize">重新初始化尺寸</el-button>
      <el-button @click="handleUpdateSize">更新容器尺寸</el-button>
      <el-button @click="toggleFullScreen">切换全屏模式</el-button>
    </div>

    <!-- 状态显示 -->
    <div class="status">
      <p><strong>当前状态：</strong></p>
      <p>全屏模式: {{ fullScreen ? '开启' : '关闭' }}</p>
      <p>缩放信息: {{ scaleInfo }}</p>
      <p>容器尺寸: {{ containerSize }}</p>
    </div>

    <!-- 大屏容器 -->
    <ZScreenContainer
      ref="screenRef"
      :width="1920"
      :height="1080"
      :full-screen="fullScreen"
      :auto-scale="true"
      :delay="300"
      @scalechange="handleScaleChange"
      class="demo-screen"
    >
      <div class="screen-content">
        <h1>大屏内容区域</h1>
        <div class="content-grid">
          <div class="content-item">
            <h3>图表区域 1</h3>
            <div class="chart-placeholder">图表内容</div>
          </div>
          <div class="content-item">
            <h3>图表区域 2</h3>
            <div class="chart-placeholder">图表内容</div>
          </div>
          <div class="content-item">
            <h3>图表区域 3</h3>
            <div class="chart-placeholder">图表内容</div>
          </div>
          <div class="content-item">
            <h3>图表区域 4</h3>
            <div class="chart-placeholder">图表内容</div>
          </div>
        </div>
        <div class="footer">
          <p>设计分辨率: 1920 x 1080</p>
          <p>当前缩放: {{ scaleInfo }}</p>
        </div>
      </div>
    </ZScreenContainer>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import ZScreenContainer from './main'

// 组件引用
const screenRef = ref<InstanceType<typeof ZScreenContainer>>()

// 响应式数据
const fullScreen = ref(false)
const scaleInfo = ref('1.0 x 1.0')
const containerSize = ref('1920 x 1080')

// 计算属性
const currentScale = computed(() => scaleInfo.value)

// 事件处理函数
const handleScaleChange = (event: CustomEvent) => {
  const { scaleX, scaleY } = event.detail
  scaleInfo.value = `${scaleX.toFixed(2)} x ${scaleY.toFixed(2)}`
  console.log('缩放变化:', event.detail)
}

// 手动触发 onResize
const handleManualResize = () => {
  if (screenRef.value) {
    screenRef.value.onResize()
    console.log('手动触发 onResize 完成')
  }
}

// 手动更新缩放
const handleUpdateScale = () => {
  if (screenRef.value) {
    screenRef.value.updateScale()
    console.log('手动更新缩放完成')
  }
}

// 重新初始化尺寸
const handleInitSize = async () => {
  if (screenRef.value) {
    await screenRef.value.initSize()
    console.log('重新初始化尺寸完成')
  }
}

// 更新容器尺寸
const handleUpdateSize = () => {
  if (screenRef.value) {
    screenRef.value.updateSize()
    console.log('更新容器尺寸完成')
  }
}

// 切换全屏模式
const toggleFullScreen = () => {
  fullScreen.value = !fullScreen.value
  // 切换后需要重新计算缩放
  setTimeout(() => {
    handleManualResize()
  }, 100)
}

// 模拟窗口大小变化
const simulateResize = () => {
  // 触发窗口 resize 事件
  window.dispatchEvent(new Event('resize'))
}

// 页面加载完成后的初始化
const onMounted = () => {
  console.log('ZScreenContainer 组件已挂载')
  
  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    console.log('窗口大小发生变化')
  })
}
</script>

<style scoped>
.screen-container-example {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
}

.controls {
  margin-bottom: 20px;
  padding: 15px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.controls .el-button {
  margin-right: 10px;
  margin-bottom: 10px;
}

.status {
  margin-bottom: 20px;
  padding: 15px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.status p {
  margin: 5px 0;
  font-size: 14px;
}

.demo-screen {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.screen-content {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  color: white;
  padding: 40px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

.screen-content h1 {
  text-align: center;
  margin-bottom: 40px;
  font-size: 48px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.content-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 30px;
  flex: 1;
  margin-bottom: 40px;
}

.content-item {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.content-item h3 {
  margin-bottom: 15px;
  font-size: 24px;
  text-align: center;
}

.chart-placeholder {
  height: 200px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  border: 2px dashed rgba(255, 255, 255, 0.3);
}

.footer {
  text-align: center;
  padding: 20px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
}

.footer p {
  margin: 5px 0;
  font-size: 16px;
}
</style>
