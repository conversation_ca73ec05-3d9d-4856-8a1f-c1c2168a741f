import { useRouter } from 'vue-router'
import { computed } from 'vue'

interface NavigationOptions {
  fallbackRoute?: string
  confirmBeforeLeave?: boolean
  confirmMessage?: string
}

export function useNavigation(options: NavigationOptions = {}) {
  const router = useRouter()

  // 默认配置
  const defaultOptions = {
    fallbackRoute: '/',
    confirmBeforeLeave: false,
    confirmMessage: '确定要离开当前页面吗？'
  }

  // 合并配置
  const config = { ...defaultOptions, ...options }

  // 检查是否有历史记录
  const hasHistory = computed(() => window.history.length > 1)

  /**
   * 返回上一页
   * @param forceFallback 是否强制使用fallback路由
   */
  const goBack = (forceFallback: boolean = false) => {
    if (forceFallback || !hasHistory.value) {
      return router.push(config.fallbackRoute)
    }

    if (config.confirmBeforeLeave && !confirm(config.confirmMessage)) {
      return
    }

    router.back()
  }

  /**
   * 返回指定步数的历史记录
   * @param delta 步数 (正数前进，负数后退)
   */
  const go = (delta: number) => {
    router.go(delta)
  }

  return {
    goBack,
    go,
    hasHistory
  }
}
