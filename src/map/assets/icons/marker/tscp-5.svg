<?xml version="1.0" encoding="UTF-8"?>
<svg width="31px" height="45px" viewBox="0 0 31 45" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 54.1 (76490) - https://sketchapp.com -->
    <title>樱桃</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <path d="M16.1111111,41.1111111 C13.3496911,41.1111111 11.1111111,40.6136489 11.1111111,40 C11.1111111,39.3863511 13.3496911,38.8888889 16.1111111,38.8888889 C18.8725311,38.8888889 21.1111111,39.3863511 21.1111111,40 C21.1111111,40.6136489 18.8725311,41.1111111 16.1111111,41.1111111 C16.1111111,41.1111111 16.1111111,41.1111111 16.1111111,41.1111111 Z" id="path-1"></path>
        <filter x="-19.6%" y="-88.1%" width="139.2%" height="276.3%" filterUnits="objectBoundingBox" id="filter-2">
            <feMorphology radius="0.301285714" operator="erode" in="SourceAlpha" result="shadowSpreadInner1"></feMorphology>
            <feGaussianBlur stdDeviation="0.7532143" in="shadowSpreadInner1" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="1.5064286" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 0.815686285   0 0 0 0 0.815686285  0 0 0 0.239215687 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <path d="M16,0 C7.7179,0 1,6.7 1,14.96 C1,18.24 2.06284,21.28 3.86764,23.76 C3.86764,23.76 7.91176,29.1733333 16,40 L27.87166,24.12 L27.85162,24.12 C29.81686,21.58 31,18.42 31,14.96 C31,14.96 31,14.96 31,14.96 C31,6.7 24.2821,0 16,0 C16,0 16,0 16,0 Z" id="path-3"></path>
        <filter x="-27.6%" y="-20.7%" width="155.2%" height="141.4%" filterUnits="objectBoundingBox" id="filter-4">
            <feGaussianBlur stdDeviation="6.77892876" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="1.5064286" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 0.266666681   0 0 0 0 0.0549019612  0 0 0 1 0" type="matrix" in="shadowInnerInner1" result="shadowMatrixInner1"></feColorMatrix>
            <feGaussianBlur stdDeviation="3.0128572" in="SourceAlpha" result="shadowBlurInner2"></feGaussianBlur>
            <feOffset dx="0" dy="1.5064286" in="shadowBlurInner2" result="shadowOffsetInner2"></feOffset>
            <feComposite in="shadowOffsetInner2" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner2"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 1 0" type="matrix" in="shadowInnerInner2" result="shadowMatrixInner2"></feColorMatrix>
            <feGaussianBlur stdDeviation="7.53214312" in="SourceAlpha" result="shadowBlurInner3"></feGaussianBlur>
            <feOffset dx="0" dy="1.5064286" in="shadowBlurInner3" result="shadowOffsetInner3"></feOffset>
            <feComposite in="shadowOffsetInner3" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner3"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 0.4627451   0 0 0 0 0.4627451  0 0 0 0.501960814 0" type="matrix" in="shadowInnerInner3" result="shadowMatrixInner3"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixInner1"></feMergeNode>
                <feMergeNode in="shadowMatrixInner2"></feMergeNode>
                <feMergeNode in="shadowMatrixInner3"></feMergeNode>
            </feMerge>
        </filter>
    </defs>
    <g id="图标板" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="特色产品图标" transform="translate(-29.000000, -90.000000)">
            <g id="樱桃" transform="translate(29.000000, 90.000000)">
                <path d="M16.1111111,44.4444444 C8.4405,44.4444444 2.22222222,42.4545956 2.22222222,40 C2.22222222,37.5454044 8.4405,35.5555556 16.1111111,35.5555556 C23.7817222,35.5555556 30,37.5454044 30,40 C30,42.4545956 23.7817222,44.4444444 16.1111111,44.4444444 C16.1111111,44.4444444 16.1111111,44.4444444 16.1111111,44.4444444 Z" id="椭圆形备份-8" stroke="#FF7575" stroke-width="0.602571428" fill-opacity="0.101960786" fill="#FF4C4C" stroke-dasharray="1,3"></path>
                <g id="椭圆形">
                    <use fill-opacity="0.58431375" fill="#FF4C4C" fill-rule="evenodd" xlink:href="#path-1"></use>
                    <use fill="black" fill-opacity="1" filter="url(#filter-2)" xlink:href="#path-1"></use>
                    <use stroke="#FF7575" stroke-width="0.602571428" xlink:href="#path-1"></use>
                </g>
                <g id="路径">
                    <use fill="#981919" fill-rule="evenodd" xlink:href="#path-3"></use>
                    <use fill="black" fill-opacity="1" filter="url(#filter-4)" xlink:href="#path-3"></use>
                    <path stroke="#FF8484" stroke-width="0.7532143" d="M27.1199,24.4966071 L27.87166,24.4966071 L27.87166,24.12 L27.5700251,23.8945021 L27.1199,24.4966071 Z M16.0001048,39.3708832 L15.6983651,39.7745021 L16.30171,39.7746026 L16.0001048,39.3708832 Z" stroke-linejoin="square"></path>
                </g>
                <g id="shipin-shuiguo-yingtao" transform="translate(5.000000, 3.000000)" fill="#FFFFFF" fill-rule="nonzero">
                    <path d="M16.6425742,10.9175622 C16.1875151,10.9175622 15.7486134,10.9886033 15.3362387,11.1201517 C14.8346367,10.0204854 14.7736246,8.9009767 15.1568195,7.77877331 C15.3487787,7.21656925 15.6217659,6.75259779 15.8530334,6.42262443 L16.4057602,6.96425109 C16.6162883,7.17076003 16.8878286,7.27315711 17.1591277,7.27315711 C17.4434492,7.27315711 17.7272884,7.16047133 17.9402281,6.93607964 C18.3564612,6.49782994 18.3439212,5.80015798 17.9127364,5.37734133 L12.7404485,0.30893088 C12.3090226,-0.113640798 11.6222138,-0.101147374 11.2059806,0.336857356 C10.7897475,0.775107056 10.8022875,1.47277902 11.2334723,1.89559567 L12.1556482,2.79928664 C10.9443277,3.22136838 9.87938816,3.82252252 8.98156888,4.59270534 C8.10231853,5.34696516 7.38319496,6.26143477 6.84445516,7.31063738 C6.51744927,7.94755701 6.29727495,8.55557029 6.14920591,9.07735445 C6.05732594,9.072945 5.96496365,9.07049531 5.87187791,9.07049531 C2.62882833,9.07049531 0,11.7409033 0,15.0352477 C0,18.329592 2.62882833,21 5.87187791,21 C9.11492748,21 11.7435147,18.329592 11.7435147,15.0352477 C11.7435147,12.2607278 9.87842354,9.92862203 7.35232714,9.26181617 C7.47917456,8.84610363 7.65883488,8.37233341 7.91325318,7.87700591 C8.37940538,6.96964041 9.00037896,6.17937013 9.75929307,5.52848726 C10.6848451,4.73454244 11.8240604,4.143677 13.1460709,3.76985417 L14.9766769,5.56376281 C14.6769215,5.96820677 14.30072,6.57083073 14.0356909,7.32460062 C13.6623833,8.38654162 13.4566783,9.91661854 14.247666,11.6456104 C13.0655252,12.4371055 12.2851483,13.7976638 12.2851483,15.3436637 C12.2851483,17.7882103 14.2360905,19.7700102 16.6425742,19.7700102 C19.0490578,19.7700102 21,17.7882103 21,15.3436637 C21,12.8991172 19.0490578,10.9175622 16.6425742,10.9175622 Z M4.0984256,12.5711036 C3.5442519,12.6323459 2.84538533,13.249178 2.7281841,14.0367536 C2.69201089,14.279518 2.48630585,14.453691 2.2519034,14.453691 C2.22802908,14.453691 2.20367244,14.4519763 2.17931581,14.4480567 C1.91597478,14.4076369 1.73462638,14.1580133 1.77441692,13.8905071 C1.92345058,12.8890735 2.81427636,11.72743 3.99424674,11.5971065 C4.25903469,11.5677102 4.49705447,11.7622157 4.52575189,12.0311917 C4.55444931,12.3001678 4.36321356,12.5419523 4.0984256,12.5711036 L4.0984256,12.5711036 Z" id="形状"></path>
                </g>
            </g>
        </g>
    </g>
</svg>